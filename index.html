<!DOCTYPE html>
<html lang="fr">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ClinicPro - Système de Gestion Médicale</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        /* Header */
        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 20px 30px;
            margin-bottom: 30px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .logo-icon {
            width: 50px;
            height: 50px;
            background: linear-gradient(45deg, #4CAF50, #45a049);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
            font-weight: bold;
        }

        .logo h1 {
            color: #2c3e50;
            font-size: 28px;
            font-weight: 700;
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .user-avatar {
            width: 45px;
            height: 45px;
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            font-size: 16px;
        }

        .welcome-text {
            color: #2c3e50;
            font-weight: 600;
        }

        /* Statistics Cards */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 25px;
            margin-bottom: 40px;
        }

        .stat-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            cursor: pointer;
            position: relative;
            overflow: hidden;
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--accent-color);
            transform: scaleX(0);
            transition: transform 0.3s ease;
        }

        .stat-card:hover::before {
            transform: scaleX(1);
        }

        .stat-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
        }

        .stat-card.patients {
            --accent-color: #3498db;
        }

        .stat-card.appointments {
            --accent-color: #e74c3c;
        }

        .stat-card.revenue {
            --accent-color: #2ecc71;
        }

        .stat-card.doctors {
            --accent-color: #f39c12;
        }

        .stat-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .stat-icon {
            width: 60px;
            height: 60px;
            border-radius: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 28px;
            background: var(--accent-color);
        }

        .stat-number {
            font-size: 36px;
            font-weight: 700;
            color: #2c3e50;
            margin-bottom: 8px;
        }

        .stat-label {
            color: #7f8c8d;
            font-size: 16px;
            font-weight: 500;
        }

        .stat-change {
            font-size: 14px;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .stat-change.positive {
            color: #27ae60;
        }

        .stat-change.negative {
            color: #e74c3c;
        }

        /* Main Content */
        .main-content {
            display: grid;
            grid-template-columns: 1fr 400px;
            gap: 30px;
        }

        .content-section {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .section-title {
            color: #2c3e50;
            font-size: 24px;
            font-weight: 700;
            margin-bottom: 25px;
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .section-icon {
            width: 35px;
            height: 35px;
            background: linear-gradient(45deg, #667eea, #764ba2);
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 18px;
        }

        /* Quick Actions */
        .quick-actions {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 20px;
        }

        .action-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 15px;
            padding: 25px 20px;
            color: white;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 10px;
            text-decoration: none;
        }

        .action-btn:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 30px rgba(102, 126, 234, 0.4);
        }

        .action-btn-icon {
            font-size: 32px;
        }

        /* Appointments List */
        .appointment-item {
            display: flex;
            align-items: center;
            padding: 20px;
            border-radius: 15px;
            margin-bottom: 15px;
            background: #f8f9fa;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .appointment-item:hover {
            background: #e9ecef;
            transform: translateX(10px);
        }

        .appointment-time {
            background: #667eea;
            color: white;
            padding: 10px 15px;
            border-radius: 10px;
            font-weight: 600;
            min-width: 80px;
            text-align: center;
        }

        .appointment-details {
            flex: 1;
            margin-left: 20px;
        }

        .patient-name {
            font-weight: 600;
            color: #2c3e50;
            font-size: 16px;
        }

        .doctor-name {
            color: #7f8c8d;
            font-size: 14px;
            margin-top: 5px;
        }

        .appointment-status {
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
        }

        .status-confirmed {
            background: #d4edda;
            color: #155724;
        }

        .status-pending {
            background: #fff3cd;
            color: #856404;
        }

        /* Responsive Design */
        @media (max-width: 1200px) {
            .main-content {
                grid-template-columns: 1fr;
            }
        }

        @media (max-width: 768px) {
            .header {
                flex-direction: column;
                gap: 20px;
                text-align: center;
            }

            .stats-grid {
                grid-template-columns: 1fr;
            }

            .quick-actions {
                grid-template-columns: 1fr;
            }

            .appointment-item {
                flex-direction: column;
                text-align: center;
                gap: 15px;
            }

            .appointment-details {
                margin-left: 0;
            }
        }

        /* Animations */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }

            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .stat-card {
            animation: fadeInUp 0.6s ease forwards;
        }

        .stat-card:nth-child(1) {
            animation-delay: 0.1s;
        }

        .stat-card:nth-child(2) {
            animation-delay: 0.2s;
        }

        .stat-card:nth-child(3) {
            animation-delay: 0.3s;
        }

        .stat-card:nth-child(4) {
            animation-delay: 0.4s;
        }
    </style>
</head>

<body>
    <div class="container">
        <!-- Header -->
        <header class="header">
            <div class="logo">
                <div class="logo-icon">C+</div>
                <h1>ClinicPro</h1>
            </div>
            <div class="user-info">
                <div class="welcome-text">Bienvenue, Dr. Martin</div>
                <div class="user-avatar">DM</div>
            </div>
        </header>

        <!-- Statistics Cards -->
        <div class="stats-grid">
            <div class="stat-card patients">
                <div class="stat-header">
                    <div>
                        <div class="stat-number">1,247</div>
                        <div class="stat-label">Patients Totaux</div>
                        <div class="stat-change positive">↗ +12% ce mois</div>
                    </div>
                    <div class="stat-icon">👥</div>
                </div>
            </div>

            <div class="stat-card appointments">
                <div class="stat-header">
                    <div>
                        <div class="stat-number">47</div>
                        <div class="stat-label">RDV Aujourd'hui</div>
                        <div class="stat-change positive">↗ +8% cette semaine</div>
                    </div>
                    <div class="stat-icon">📅</div>
                </div>
            </div>

            <div class="stat-card revenue">
                <div class="stat-header">
                    <div>
                        <div class="stat-number">€12,450</div>
                        <div class="stat-label">Revenus ce Mois</div>
                        <div class="stat-change positive">↗ +15% vs mois dernier</div>
                    </div>
                    <div class="stat-icon">💰</div>
                </div>
            </div>

            <div class="stat-card doctors">
                <div class="stat-header">
                    <div>
                        <div class="stat-number">8</div>
                        <div class="stat-label">Médecins Actifs</div>
                        <div class="stat-change positive">↗ +1 nouveau</div>
                    </div>
                    <div class="stat-icon">⚕️</div>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="main-content">
            <!-- Quick Actions -->
            <div class="content-section">
                <h2 class="section-title">
                    <div class="section-icon">⚡</div>
                    Actions Rapides
                </h2>
                <div class="quick-actions">
                    <button class="action-btn" onclick="openNewPatient()">
                        <div class="action-btn-icon">👤</div>
                        Nouveau Patient
                    </button>
                    <button class="action-btn" onclick="openNewAppointment()">
                        <div class="action-btn-icon">📅</div>
                        Nouveau RDV
                    </button>
                    <button class="action-btn" onclick="openPrescription()">
                        <div class="action-btn-icon">💊</div>
                        Ordonnance
                    </button>
                    <button class="action-btn" onclick="openBilling()">
                        <div class="action-btn-icon">💳</div>
                        Facturation
                    </button>
                    <button class="action-btn" onclick="openReports()">
                        <div class="action-btn-icon">📊</div>
                        Rapports
                    </button>
                    <button class="action-btn" onclick="openSettings()">
                        <div class="action-btn-icon">⚙️</div>
                        Paramètres
                    </button>
                </div>
            </div>

            <!-- Today's Appointments -->
            <div class="content-section">
                <h2 class="section-title">
                    <div class="section-icon">📋</div>
                    RDV Aujourd'hui
                </h2>
                <div class="appointments-list">
                    <div class="appointment-item">
                        <div class="appointment-time">09:00</div>
                        <div class="appointment-details">
                            <div class="patient-name">Marie Dubois</div>
                            <div class="doctor-name">Dr. Bernard - Cardiologie</div>
                        </div>
                        <div class="appointment-status status-confirmed">Confirmé</div>
                    </div>

                    <div class="appointment-item">
                        <div class="appointment-time">09:30</div>
                        <div class="appointment-details">
                            <div class="patient-name">Pierre Martin</div>
                            <div class="doctor-name">Dr. Dupont - Médecine Générale</div>
                        </div>
                        <div class="appointment-status status-pending">En Attente</div>
                    </div>

                    <div class="appointment-item">
                        <div class="appointment-time">10:00</div>
                        <div class="appointment-details">
                            <div class="patient-name">Sophie Leroy</div>
                            <div class="doctor-name">Dr. Martin - Pédiatrie</div>
                        </div>
                        <div class="appointment-status status-confirmed">Confirmé</div>
                    </div>

                    <div class="appointment-item">
                        <div class="appointment-time">10:30</div>
                        <div class="appointment-details">
                            <div class="patient-name">Jean Rousseau</div>
                            <div class="doctor-name">Dr. Bernard - Cardiologie</div>
                        </div>
                        <div class="appointment-status status-confirmed">Confirmé</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Animation on scroll
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.animationPlayState = 'running';
                }
            });
        }, observerOptions);

        document.querySelectorAll('.stat-card').forEach(card => {
            observer.observe(card);
        });

        // Quick action functions
        function openNewPatient() {
            alert('Ouvrir le formulaire d\'ajout de nouveau patient');
        }

        function openNewAppointment() {
            alert('Ouvrir le planificateur de rendez-vous');
        }

        function openPrescription() {
            alert('Ouvrir le module de prescription');
        }

        function openBilling() {
            alert('Ouvrir le module de facturation');
        }

        function openReports() {
            alert('Ouvrir les rapports et statistiques');
        }

        function openSettings() {
            alert('Ouvrir les paramètres du système');
        }

        // Real-time clock update
        function updateClock() {
            const now = new Date();
            const timeString = now.toLocaleTimeString('fr-FR');
            const dateString = now.toLocaleDateString('fr-FR', {
                weekday: 'long',
                year: 'numeric',
                month: 'long',
                day: 'numeric'
            });

            // You can add a clock element if needed
        }

        setInterval(updateClock, 1000);
        updateClock();

        // Add some interactive effects
        document.querySelectorAll('.stat-card').forEach(card => {
            card.addEventListener('mouseenter', () => {
                card.style.transform = 'translateY(-10px) scale(1.02)';
            });

            card.addEventListener('mouseleave', () => {
                card.style.transform = 'translateY(0) scale(1)';
            });
        });

        // Animate numbers on load
        function animateNumbers() {
            const numbers = document.querySelectorAll('.stat-number');
            numbers.forEach(num => {
                const finalNumber = parseInt(num.textContent.replace(/[^0-9]/g, ''));
                const isEuro = num.textContent.includes('€');
                let currentNumber = 0;
                const increment = finalNumber / 50;

                const timer = setInterval(() => {
                    currentNumber += increment;
                    if (currentNumber >= finalNumber) {
                        currentNumber = finalNumber;
                        clearInterval(timer);
                    }

                    if (isEuro) {
                        num.textContent = '€' + Math.floor(currentNumber).toLocaleString();
                    } else {
                        num.textContent = Math.floor(currentNumber).toLocaleString();
                    }
                }, 30);
            });
        }

        // Start number animation after page load
        window.addEventListener('load', () => {
            setTimeout(animateNumbers, 500);
        });
    </script>
</body>

</html>